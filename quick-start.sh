#!/bin/bash

# TDLoad 快速启动脚本
# Quick Start Script for TDLoad

set -e

echo "🚀 TDLoad 快速启动"
echo "=================="

# 检查必需的环境变量
if [ -z "$BOT_TOKEN" ]; then
    echo "❌ 请设置 BOT_TOKEN 环境变量"
    echo "   export BOT_TOKEN=\"your_bot_token_here\""
    exit 1
fi

if [ -z "$ADMIN_USER_ID" ]; then
    echo "❌ 请设置 ADMIN_USER_ID 环境变量"
    echo "   export ADMIN_USER_ID=\"your_user_id_here\""
    exit 1
fi

echo "✅ 环境变量检查通过"
echo "   BOT_TOKEN: ${BOT_TOKEN:0:10}..."
echo "   ADMIN_USER_ID: $ADMIN_USER_ID"

# 创建数据目录
echo ""
echo "📁 创建数据目录..."
mkdir -p data
echo "✅ 数据目录创建完成: $(pwd)/data"

# 询问是否预创建数据库
echo ""
read -p "🗄️ 是否预创建数据库文件? (y/N): " create_db
if [[ $create_db =~ ^[Yy]$ ]]; then
    echo "📊 创建数据库文件..."
    python3 init_db.py data/tdload.db $ADMIN_USER_ID
    echo "✅ 数据库文件创建完成"
else
    echo "⏭️ 跳过数据库预创建（机器人启动时会自动创建）"
fi

# 检查Docker
echo ""
echo "🐳 检查Docker..."
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    exit 1
fi

if ! docker info &> /dev/null; then
    echo "❌ Docker未运行，请启动Docker"
    exit 1
fi

echo "✅ Docker检查通过"

# 停止现有容器（如果存在）
echo ""
echo "🛑 停止现有容器..."
if docker ps -a --format "table {{.Names}}" | grep -q "^tdload$"; then
    docker stop tdload || true
    docker rm tdload || true
    echo "✅ 现有容器已停止并删除"
else
    echo "ℹ️ 没有找到现有容器"
fi

# 启动新容器
echo ""
echo "🚀 启动TDLoad容器..."

# 检查是否有本地镜像
if docker images --format "table {{.Repository}}:{{.Tag}}" | grep -q "tdload:latest"; then
    echo "📦 使用本地镜像: tdload:latest"
    IMAGE_NAME="tdload:latest"
else
    echo "📦 使用DockerHub镜像: jhxxr/tdload:latest"
    IMAGE_NAME="jhxxr/tdload:latest"
fi

docker run -d \
  --name tdload \
  --restart unless-stopped \
  -e BOT_TOKEN="$BOT_TOKEN" \
  -e ADMIN_USER_ID="$ADMIN_USER_ID" \
  -v "$(pwd)/data:/app/data" \
  $IMAGE_NAME

echo "✅ 容器启动成功"

# 等待容器启动
echo ""
echo "⏳ 等待容器启动..."
sleep 3

# 检查容器状态
if docker ps --format "table {{.Names}}\t{{.Status}}" | grep -q "tdload.*Up"; then
    echo "✅ 容器运行正常"
else
    echo "❌ 容器启动失败，查看日志:"
    docker logs tdload
    exit 1
fi

# 显示后续步骤
echo ""
echo "🎉 TDLoad 启动完成！"
echo ""
echo "📋 后续步骤:"
echo "1. 与机器人私聊，发送 /start 命令测试"
echo "2. 添加WebDAV配置:"
echo "   /add_webdav MyCloud https://your-webdav-server.com username password /path/"
echo "3. 添加授权用户:"
echo "   /add_user user_id_here"
echo ""
echo "🔧 管理命令:"
echo "   查看日志: docker logs -f tdload"
echo "   停止服务: docker stop tdload"
echo "   重启服务: docker restart tdload"
echo ""
echo "📊 数据目录: $(pwd)/data"
echo "🗄️ 数据库文件: $(pwd)/data/tdload.db"
