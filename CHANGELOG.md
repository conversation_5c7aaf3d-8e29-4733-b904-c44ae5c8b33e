# 更新日志

## [2.0.0] - 2025-09-06

### 🎉 重大更新

#### ✨ 新功能
- **SQLite数据库**: 使用SQLite替代JSON文件存储数据，提供更好的数据管理
- **多WebDAV支持**: 支持配置多个WebDAV服务器，用户可选择上传目标
- **智能上传**: 单个WebDAV时自动上传，多个时显示选择界面
- **WebDAV管理命令**: 通过机器人命令管理WebDAV配置，无需重启
- **内联键盘**: 使用Telegram内联键盘提供更好的用户体验

#### 🔧 改进
- **环境变量简化**: 移除WebDAV相关环境变量，改为命令管理
- **数据持久化**: 所有配置数据存储在SQLite数据库中
- **错误处理**: 改进错误处理和用户反馈
- **日志记录**: 增强日志记录，便于问题排查

#### 📋 新增命令
- `/add_webdav <名称> <地址> <用户名> <密码> [路径]` - 添加WebDAV配置
- `/remove_webdav <名称>` - 删除WebDAV配置
- `/list_webdav` - 查看WebDAV配置列表

#### 🗑️ 移除
- 移除对环境变量 `WEBDAV_HOSTNAME`, `WEBDAV_LOGIN`, `WEBDAV_PASSWORD`, `WEBDAV_PATH` 的依赖
- 移除JSON文件存储方式

#### 🔄 迁移指南
如果你正在从v1.x版本升级：

1. **备份数据**: 备份现有的 `authorized_users.json` 文件
2. **更新环境变量**: 移除WebDAV相关环境变量
3. **重新部署**: 使用新的Docker镜像部署
4. **重新配置**: 使用 `/add_webdav` 命令重新添加WebDAV配置
5. **重新授权**: 管理员用户会自动迁移，其他用户需要重新添加

---

## [1.0.0] - 2025-09-05

### 🎉 首次发布

#### ✨ 功能
- **文件上传**: 支持上传文档、图片、视频、音频到WebDAV服务器
- **用户管理**: 管理员可以添加/移除授权用户
- **私聊限制**: 只在私聊中工作，确保安全性
- **Docker部署**: 完整的Docker化部署方案
- **GitHub Actions**: 自动构建和推送到DockerHub

#### 📋 命令
- `/start` - 开始使用机器人
- `/id` - 查看用户ID
- `/add_user <user_id>` - 添加授权用户
- `/remove_user <user_id>` - 移除授权用户
- `/list_users` - 查看授权用户列表

#### 🔧 技术特性
- 基于python-telegram-bot库
- 使用WebDAV3客户端
- JSON文件存储用户数据
- 环境变量配置
