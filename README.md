# TDLoad - Telegram WebDAV Bot

一个功能强大的Telegram机器人，支持将文件上传到WebDAV服务器，具有用户管理和权限控制功能。

## 功能特性

- 🔐 **用户权限管理**: 管理员可以控制哪些用户可以使用机器人
- 📁 **多WebDAV支持**: 支持配置多个WebDAV服务器，上传时可选择目标
- 🎯 **智能选择**: 单个WebDAV时自动上传，多个时提供选择界面
- 📊 **SQLite数据库**: 使用SQLite存储用户权限和WebDAV配置
- 🔒 **私聊限制**: 机器人只在私聊中工作，确保安全性
- 🆔 **用户ID查询**: 所有用户都可以查询自己的Telegram用户ID
- 🐳 **Docker部署**: 完整的Docker化部署方案
- 🚀 **CI/CD**: 自动化构建和部署到DockerHub

## 快速开始

### 1. 准备工作

1. **创建Telegram机器人**：
   - 与 @BotFather 对话创建机器人
   - 获取Bot Token

2. **获取你的Telegram用户ID**：
   - 与 @userinfobot 对话获取你的用户ID
   - 或者先运行机器人，使用 `/id` 命令查看

3. **准备WebDAV服务器信息**（可在机器人运行后通过命令添加）：
   - WebDAV服务器地址
   - 用户名和密码
   - 保存路径

### 2. 一键启动（推荐）

```bash
# 设置环境变量
export BOT_TOKEN="your_telegram_bot_token_here"
export ADMIN_USER_ID="your_telegram_user_id_here"

# 运行快速启动脚本
./quick-start.sh
```

### 3. 使用Docker Compose部署

1. 克隆项目：
```bash
git clone <your-repo-url>
cd tdload
```

2. （可选）预创建数据库文件：
```bash
# 创建空数据库（可选，机器人会自动创建）
python init_db.py data/tdload.db your_telegram_user_id_here
```

3. 设置环境变量并启动服务：
```bash
export BOT_TOKEN="your_telegram_bot_token_here"
export ADMIN_USER_ID="your_telegram_user_id_here"

docker-compose up -d
```

4. 添加WebDAV配置（通过机器人命令）：
```
/add_webdav MyCloud https://your-webdav-server.com username password /path/
```

### 4. 使用Docker直接部署

```bash
# 创建数据目录
mkdir -p data

# （可选）预创建数据库
python init_db.py data/tdload.db your_user_id

# 运行容器
docker run -d \
  --name tdload \
  --restart unless-stopped \
  -e BOT_TOKEN="your_bot_token" \
  -e ADMIN_USER_ID="your_user_id" \
  -v $(pwd)/data:/app/data \
  your-dockerhub-username/tdload:latest
```

然后通过机器人命令添加WebDAV配置：
```
/add_webdav MyCloud https://your-webdav-server.com username password /path/
```

### 4. 使用示例脚本快速部署

1. 下载并编辑示例脚本：
```bash
wget https://raw.githubusercontent.com/your-username/tdload/main/start-example.sh
chmod +x start-example.sh
# 编辑脚本中的环境变量
nano start-example.sh
```

2. 运行脚本：
```bash
./start-example.sh
```

## 环境变量说明

| 变量名 | 必需 | 说明 |
|--------|------|------|
| `BOT_TOKEN` | ✅ | Telegram机器人Token |
| `ADMIN_USER_ID` | ✅ | 管理员的Telegram用户ID |
| `DATA_DIR` | ❌ | 数据目录（默认: /app/data） |

**注意**: WebDAV配置现在通过机器人命令管理，不再需要环境变量。

## 机器人命令

### 普通用户命令
- `/start` - 开始使用机器人，查看状态和帮助信息
- `/id` - 查看自己的Telegram用户ID

### 管理员命令

**用户管理:**
- `/add_user <user_id>` - 添加用户到授权列表
- `/remove_user <user_id>` - 从授权列表中移除用户
- `/list_users` - 查看所有授权用户

**WebDAV管理:**
- `/add_webdav <名称> <地址> <用户名> <密码> [路径]` - 添加WebDAV配置
- `/remove_webdav <名称>` - 删除WebDAV配置
- `/list_webdav` - 查看WebDAV配置列表

**示例:**
```
/add_webdav MyCloud https://cloud.example.com user123 pass123 /uploads/
/add_webdav Backup https://backup.example.com admin secret /backup/
```

## 数据库管理

### 自动初始化
机器人启动时会自动创建SQLite数据库和必要的表结构。

### 手动初始化（可选）
如果您希望预先创建数据库文件：

```bash
# 创建空数据库
python init_db.py data/tdload.db

# 创建数据库并添加管理员用户
python init_db.py data/tdload.db 123456789
```

### 数据持久化
- 数据库文件：`/app/data/tdload.db`
- 通过Docker卷挂载实现数据持久化
- 包含用户权限和WebDAV配置信息

## 使用说明

1. **获取用户ID**: 任何用户都可以通过`/id`命令查看自己的用户ID
2. **配置WebDAV**: 管理员使用`/add_webdav`命令添加WebDAV服务器配置
3. **授权用户**: 管理员使用`/add_user`命令添加用户到授权列表
4. **上传文件**: 授权用户直接发送文件给机器人
   - 单个WebDAV配置时：自动上传
   - 多个WebDAV配置时：显示选择界面
5. **支持的文件类型**: 文档、图片、视频、音频、语音消息

## 开发和构建

### 本地开发

1. 安装依赖：
```bash
pip install -r requirements.txt
```

2. 设置环境变量并运行：
```bash
export BOT_TOKEN="your_bot_token"
export ADMIN_USER_ID="your_user_id"
python main.py
```

3. 通过机器人命令添加WebDAV配置：
```
/add_webdav MyCloud https://your-webdav-server.com username password /path/
```

### 构建Docker镜像

```bash
docker build -t tdload .
```

## GitHub Actions设置

要启用自动构建和推送到DockerHub，需要在GitHub仓库中设置以下Secrets：

### 设置步骤：

1. **创建DockerHub访问令牌**：
   - 登录DockerHub
   - 进入 Account Settings → Security → New Access Token
   - 创建一个新的访问令牌

2. **在GitHub仓库中设置Secrets**：
   - 进入你的GitHub仓库
   - 点击 Settings → Secrets and variables → Actions
   - 添加以下Repository secrets：
     - `DOCKERHUB_USERNAME`: 你的DockerHub用户名
     - `DOCKERHUB_TOKEN`: 刚才创建的DockerHub访问令牌

3. **推送代码触发构建**：
   - 推送到 `main` 或 `master` 分支会自动构建并推送镜像
   - 创建版本标签（如 `v1.0.0`）会构建对应版本的镜像

## 安全注意事项

- 机器人只接受私聊消息，不会在群组中工作
- 只有授权用户才能上传文件
- 管理员权限不能被移除
- 所有敏感信息通过环境变量配置
- 使用非root用户运行容器

## 故障排除

### 常见问题

1. **机器人无响应**
   - 检查Bot Token是否正确
   - 确认网络连接正常

2. **上传失败**
   - 检查WebDAV服务器配置
   - 确认WebDAV路径存在且有写入权限

3. **权限问题**
   - 确认ADMIN_USER_ID设置正确
   - 检查用户是否在授权列表中

### 查看日志

```bash
# Docker Compose
docker-compose logs -f

# Docker
docker logs -f tdload
```

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request！
