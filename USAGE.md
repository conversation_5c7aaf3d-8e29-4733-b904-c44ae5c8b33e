# TDLoad 使用指南

## 快速开始

### 1. 部署机器人

```bash
# 设置环境变量
export BOT_TOKEN="your_telegram_bot_token"
export ADMIN_USER_ID="your_telegram_user_id"

# 启动机器人
docker run -d \
  --name tdload \
  --restart unless-stopped \
  -e BOT_TOKEN="$BOT_TOKEN" \
  -e ADMIN_USER_ID="$ADMIN_USER_ID" \
  -v $(pwd)/data:/app/data \
  your-dockerhub-username/tdload:latest
```

### 2. 配置WebDAV

与机器人私聊，使用以下命令添加WebDAV配置：

```
/add_webdav MyCloud https://cloud.example.com user123 pass123 /uploads/
/add_webdav Backup https://backup.example.com admin secret /backup/
```

### 3. 添加授权用户

```
/add_user 123456789
/add_user 987654321
```

## 命令详解

### 普通用户命令

- `/start` - 查看机器人状态和帮助信息
- `/id` - 查看自己的Telegram用户ID

### 管理员命令

#### 用户管理
- `/add_user <user_id>` - 添加授权用户
- `/remove_user <user_id>` - 移除授权用户  
- `/list_users` - 查看授权用户列表

#### WebDAV管理
- `/add_webdav <名称> <地址> <用户名> <密码> [路径]` - 添加WebDAV配置
- `/remove_webdav <名称>` - 删除WebDAV配置
- `/list_webdav` - 查看WebDAV配置列表

## 使用场景

### 场景1: 单个WebDAV服务器

如果只配置了一个WebDAV服务器，用户发送文件时会自动上传到该服务器。

### 场景2: 多个WebDAV服务器

如果配置了多个WebDAV服务器，用户发送文件时会显示选择界面：

```
📁 收到文件: document.pdf
请选择上传目标:

🎯 选择上传目标:
┌─────────────────────────────────────┐
│ MyCloud (https://cloud.example.com/uploads/) │
│ Backup (https://backup.example.com/backup/)  │
└─────────────────────────────────────┘
```

用户点击按钮选择目标后，文件会上传到对应的WebDAV服务器。

## 支持的文件类型

- 📄 文档 (PDF, DOC, TXT, 等)
- 🖼️ 图片 (JPG, PNG, GIF, 等)
- 🎥 视频 (MP4, AVI, MOV, 等)
- 🎵 音频 (MP3, WAV, FLAC, 等)
- 🎤 语音消息

## 数据存储

- 用户权限和WebDAV配置存储在SQLite数据库中
- 数据库文件位于 `/app/data/tdload.db`
- 通过Docker卷挂载实现数据持久化

## 安全特性

- 只接受私聊消息，不在群组中工作
- 用户权限控制，只有授权用户可以上传文件
- 管理员权限保护，无法删除管理员账户
- WebDAV密码加密存储在数据库中

## 故障排除

### 常见问题

1. **机器人无响应**
   - 检查Bot Token是否正确
   - 确认网络连接正常

2. **上传失败**
   - 检查WebDAV服务器配置
   - 确认WebDAV路径存在且有写入权限
   - 查看机器人日志: `docker logs tdload`

3. **权限问题**
   - 确认ADMIN_USER_ID设置正确
   - 检查用户是否在授权列表中

### 查看日志

```bash
# 实时查看日志
docker logs -f tdload

# 查看最近的日志
docker logs --tail=50 tdload
```

### 备份数据

```bash
# 备份数据库
cp data/tdload.db data/tdload.db.backup

# 恢复数据库
cp data/tdload.db.backup data/tdload.db
```
