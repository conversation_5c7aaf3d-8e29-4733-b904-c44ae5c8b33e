#!/usr/bin/env python3
"""
TDLoad Database Initialization Script
创建空的SQLite数据库文件
"""

import os
import sqlite3
import sys

def create_database(db_path: str, admin_user_id: int = None) -> bool:
    """创建空的数据库文件"""
    
    print(f"🆕 创建数据库: {db_path}")
    
    try:
        # 确保目录存在
        db_dir = os.path.dirname(db_path)
        if db_dir:
            os.makedirs(db_dir, exist_ok=True)
            print(f"📁 创建目录: {db_dir}")
        
        # 创建数据库
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 创建授权用户表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS authorized_users (
                    user_id INTEGER PRIMARY KEY,
                    added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            print("✅ 创建表: authorized_users")
            
            # 创建WebDAV配置表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS webdav_configs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL UNIQUE,
                    hostname TEXT NOT NULL,
                    login TEXT NOT NULL,
                    password TEXT NOT NULL,
                    path TEXT NOT NULL DEFAULT '/',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            print("✅ 创建表: webdav_configs")
            
            # 添加管理员用户（如果指定）
            if admin_user_id:
                cursor.execute('INSERT OR IGNORE INTO authorized_users (user_id) VALUES (?)', (admin_user_id,))
                print(f"✅ 添加管理员用户: {admin_user_id}")
            
            conn.commit()
            
        print(f"🎉 数据库创建成功: {db_path}")
        
        # 显示文件信息
        file_size = os.path.getsize(db_path)
        print(f"📊 文件大小: {file_size} bytes")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建数据库失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 TDLoad 数据库初始化工具")
    print("=" * 40)
    
    if len(sys.argv) < 2:
        print("使用方法:")
        print("  python init_db.py <db_path> [admin_user_id]")
        print("")
        print("示例:")
        print("  python init_db.py data/tdload.db")
        print("  python init_db.py data/tdload.db 123456789")
        print("")
        print("说明:")
        print("  db_path      - 数据库文件路径")
        print("  admin_user_id - 管理员Telegram用户ID（可选）")
        sys.exit(1)
    
    db_path = sys.argv[1]
    admin_user_id = int(sys.argv[2]) if len(sys.argv) > 2 else None
    
    # 检查文件是否已存在
    if os.path.exists(db_path):
        response = input(f"⚠️ 文件已存在: {db_path}\n是否覆盖? (y/N): ")
        if response.lower() != 'y':
            print("❌ 操作已取消")
            sys.exit(1)
    
    success = create_database(db_path, admin_user_id)
    
    if success:
        print("\n🎯 下一步:")
        print("1. 将数据库文件放到Docker容器的data目录中")
        print("2. 启动TDLoad机器人")
        print("3. 使用 /add_webdav 命令添加WebDAV配置")
        print("4. 使用 /add_user 命令添加授权用户")
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
